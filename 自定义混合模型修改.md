现在需要用llamafactory这个框架来支持一个我们自定义的模型，这个模型用MMS的Wav2Vec2Model来替换Qwen2_5_omni中的audio_tower，模型结构你可以看 `模型结构和示例代码.md`。
1. **Qwen_Omni结构**：包含thinker部分（audio_tower + visual + model + lm_head）和talker部分
2. **MMS结构**：Wav2Vec2Model 包含 
    feature_extractor： 接收原始音频信号，输出512维特征
    feature_projection： 将512维特征投影到1280维
    encoder： Transformer编码器，输出1280维特征
3. **关键差异**：
    - Qwen audio_tower输入mel-spectrogram (128维提前提取的特征)，MMS输入raw audio (1维原始音频信号，内部做特征提取)
    - Qwen audio_tower输出3584维，MMS输出1280维
    - 需要适配层进行维度转换

之前你也进行了这个任务（独立创建一个文件夹来做这个工作实在是太混乱了，并且你自由发挥过度了，这些代码运行起来风险很大）但是我很不满意，现在我们重新来。

现在按照以下要求来完成源代码的修改吧（原则就是继承/复用原始代码，只做必要的改动，风险最小），完成之后就可以删掉`custom_qwen_mms`其中的py文件，仅留下如何使用以及一些相关的json、yaml等文件就可以。

注意，分步骤进行，需要规划Tasks，然后分别完成每个子项

## Transformers 层面

需要在 `transformers-4.55.0\src\transformers\models\custom_qwen_mms\` 文件夹中创建自定义模型实现

1. `modular_custom_qwen_mms.py` (需要重命名为这个)
    对应 `transformers-4.55.0\src\transformers\models\qwen2_5_omni\modular_qwen2_5_omni.py`
    自定义模型代码实现，可以复用modular_qwen2_5_omni.py中的大部分代码, 模型配置部分也需要（大部分都可以复制过来然后修改类名）
    注意，这个文件非常大，你必须先仔细分析文件结构，理清楚逻辑关系，哪些是配置类，哪些是模型定义类，以及模型和其子模型又有嵌套关系，因为我们需要定义自己的类，都可以采用继承、overwrite部分属性/方法。
2. `processing_custom_qwen_mms.py` (需要重命名为这个)
    对应 `transformers-4.55.0\src\transformers\models\qwen2_5_omni\processing_qwen2_5_omni.py`
    自定义数据处理代码实现，可以复用processing_qwen2_5_omni.py中的大部分代码
3. `__init__.py` 导入定义
    同样模仿`transformers-4.55.0\src\transformers\models\qwen2_5_omni\__init__.py`中的导入
4. `configuration_custom_qwen_mms.py` (需要重命名为这个)
    对应 `transformers-4.55.0\src\transformers\models\qwen2_5_omni\configuration_qwen2_5_omni.py`
    从`modular_custom_qwen_mms.py`自动生成的
5. `modeling_custom_qwen_mms.py` (需要重命名为这个)
    对应 `transformers-4.55.0\src\transformers\models\qwen2_5_omni\modeling_qwen2_5_omni.py`
    从`modular_custom_qwen_mms.py`自动生成的

## LLama-Factory 层面

直接在源代码上修改, 关于如何自定义模型，可以参看 `LLamaFactory模型支持.md`

1. `mm_plugin.py`（实现多模态数据的解析）
    添加自定义多模态插件
2. `template.py`
    添加自定义模板(用于注册模型模板)
3. `constants.py`
    添加模型注册（用于提供模型的下载路径）
4. `loader.py`
    添加模型加载逻辑（查找omni字样，模仿者添加几行就可以）
