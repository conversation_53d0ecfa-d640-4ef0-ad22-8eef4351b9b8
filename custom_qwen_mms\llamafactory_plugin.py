"""
Custom Qwen-MMS plugin for LLaMA-Factory multimodal data processing
Inherits from Qwen2OmniPlugin and only modifies audio processing
"""

import torch
from typing import Dict, List, Optional, Any
from llamafactory.data.mm_plugin import Qwen2OmniPlugin, register_mm_plugin
from llamafactory.extras.logging import get_logger

logger = get_logger(__name__)


class CustomQwenMMSPlugin(Qwen2OmniPlugin):
    """
    Custom plugin for Qwen-MMS multimodal data processing
    Inherits from Qwen2OmniPlugin and only changes audio processing to use MMS
    """

    def _get_mm_inputs(
        self,
        images: List[Any],
        videos: List[Any],
        audios: List[Any],
        processor: Any,
    ) -> Dict[str, torch.Tensor]:
        """
        Process multimodal inputs, using MMS for audio instead of Whisper
        """
        # Use parent class for image and video processing
        mm_inputs = {}

        # Process images and videos using parent implementation
        if images or videos:
            parent_inputs = super()._get_mm_inputs(images, videos, [], processor)
            mm_inputs.update(parent_inputs)

        # Custom audio processing using MMS
        if audios:
            try:
                # Get MMS feature extractor from processor
                audio_feature_extractor = getattr(processor, 'audio_feature_extractor', None)
                if audio_feature_extractor is None:
                    logger.warning("No MMS audio feature extractor found in processor")
                    return mm_inputs

                # Regularize audios (use parent method)
                audios_dict = self._regularize_audios(
                    audios,
                    sampling_rate=getattr(processor, "audio_sampling_rate", 16000),
                )

                # Process with MMS feature extractor
                audio_inputs = audio_feature_extractor(
                    audios_dict["audios"],
                    sampling_rate=getattr(processor, "audio_sampling_rate", 16000),
                    return_attention_mask=True,
                    padding="max_length",
                    return_tensors="pt",
                )

                # Rename to match our model's expected input names
                if "input_values" in audio_inputs:
                    audio_inputs["audio_values"] = audio_inputs.pop("input_values")  # input_values: raw signal with shape (1, num_samples)

                # Handle attention mask
                if "attention_mask" in audio_inputs:
                    audio_inputs["feature_attention_mask"] = audio_inputs.pop("attention_mask")  # attention_mask: (1, num_samples)

                mm_inputs.update(audio_inputs)
                logger.debug(f"Processed {len(audios)} audio files with MMS")

            except Exception as e:
                logger.error(f"Failed to process audios with MMS: {e}")

        return mm_inputs

def register_custom_qwen_mms_plugin():
    """Register the custom Qwen-MMS plugin"""
    register_mm_plugin("custom_qwen_mms", CustomQwenMMSPlugin)
