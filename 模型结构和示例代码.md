

###### Qwen_Omni asr & st 示例代码
```python
from transformers import Qwen2_5OmniForConditionalGeneration, Qwen2_5OmniProcessor
from qwen_omni_utils import process_mm_info

def inference(audio_path, prompt, sys_prompt):
    messages = [
        {"role": "system", "content": [{"type": "text", "text": sys_prompt}]},
        {"role": "user", "content": [
                {"type": "audio", "audio": audio_path},
                {"type": "text", "text": prompt},
            ]
        },
    ]
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    audios, images, videos = process_mm_info(messages, use_audio_in_video=True)
    inputs = processor(text=text, audio=audios, images=images, videos=videos, return_tensors="pt", padding=True, use_audio_in_video=True)
    inputs = inputs.to(model.device).to(model.dtype)

    output = model.generate(**inputs, use_audio_in_video=True, return_audio=False, thinker_max_new_tokens=256, thinker_do_sample=False)

    text = processor.batch_decode(output, skip_special_tokens=True, clean_up_tokenization_spaces=False)
    return text

# load
model = Qwen2_5OmniForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        attn_implementation="flash_attention_2",
    )
processor = Qwen2_5OmniProcessor.from_pretrained(model_path)

# asr infer
audio = librosa.load(audio_path, sr=16000)[0]
prompt = "Transcribe the English audio into text without any punctuation marks."
response = inference(audio_path, prompt=prompt, sys_prompt="You are a speech recognition model.")

# st infer
prompt = "Listen to the provided English speech and produce a translation in Chinese text."
response = inference(audio_path, prompt=prompt, sys_prompt="You are a speech translation model.")
```

###### Qwen_Omni 模型结构
```bash
Qwen2_5OmniForConditionalGeneration(
  (thinker): Qwen2_5OmniThinkerForConditionalGeneration(
    (audio_tower): Qwen2_5OmniAudioEncoder(
      (conv1): Conv1d(128, 1280, kernel_size=(3,), stride=(1,), padding=(1,))
      (conv2): Conv1d(1280, 1280, kernel_size=(3,), stride=(2,), padding=(1,))
      (positional_embedding): SinusoidsPositionEmbedding()
      (audio_bos_eos_token): Embedding(2, 3584)
      (layers): ModuleList(
        (0-31): 32 x Qwen2_5OmniAudioEncoderLayer(
          (self_attn): Qwen2_5OmniAudioAttention(
            (k_proj): Linear(in_features=1280, out_features=1280, bias=False)
            (v_proj): Linear(in_features=1280, out_features=1280, bias=True)
            (q_proj): Linear(in_features=1280, out_features=1280, bias=True)
            (out_proj): Linear(in_features=1280, out_features=1280, bias=True)
          )
          (self_attn_layer_norm): LayerNorm((1280,), eps=1e-05, elementwise_affine=True)
          (activation_fn): GELUActivation()
          (fc1): Linear(in_features=1280, out_features=5120, bias=True)
          (fc2): Linear(in_features=5120, out_features=1280, bias=True)
          (final_layer_norm): LayerNorm((1280,), eps=1e-05, elementwise_affine=True)
        )
      )
      (ln_post): LayerNorm((1280,), eps=1e-05, elementwise_affine=True)
      (avg_pooler): AvgPool1d(kernel_size=(2,), stride=(2,), padding=(0,))
      (proj): Linear(in_features=1280, out_features=3584, bias=True)
    )
    (visual): Qwen2_5OmniVisionEncoder(
      (patch_embed): Qwen2_5_VisionPatchEmbed(
        (proj): Conv3d(3, 1280, kernel_size=(2, 14, 14), stride=(2, 14, 14), bias=False)
      )
      (rotary_pos_emb): Qwen2_5_VisionRotaryEmbedding()
      (blocks): ModuleList(
        (0-31): 32 x Qwen2_5OmniVisionBlock(
          (norm1): Qwen2RMSNorm((1280,), eps=1e-06)
          (norm2): Qwen2RMSNorm((1280,), eps=1e-06)
          (attn): Qwen2_5OmniVisionAttention(
            (q): Linear(in_features=1280, out_features=1280, bias=True)
            (k): Linear(in_features=1280, out_features=1280, bias=True)
            (v): Linear(in_features=1280, out_features=1280, bias=True)
            (proj): Linear(in_features=1280, out_features=1280, bias=True)
          )
          (mlp): Qwen2_5OmniMLP(
            (gate_proj): Linear(in_features=1280, out_features=3420, bias=True)
            (up_proj): Linear(in_features=1280, out_features=3420, bias=True)
            (down_proj): Linear(in_features=3420, out_features=1280, bias=True)
            (act_fn): SiLU()
          )
        )
      )
      (merger): Qwen2_5OmniPatchMerger(
        (ln_q): Qwen2RMSNorm((1280,), eps=1e-06)
        (mlp): Sequential(
          (0): Linear(in_features=5120, out_features=5120, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=5120, out_features=3584, bias=True)
        )
      )
    )
    (model): Qwen2_5OmniThinkerTextModel(
      (embed_tokens): Embedding(152064, 3584)
      (layers): ModuleList(
        (0-27): 28 x Qwen2_5OmniDecoderLayer(
          (self_attn): Qwen2_5OmniAttention(
            (q_proj): Linear(in_features=3584, out_features=3584, bias=True)
            (k_proj): Linear(in_features=3584, out_features=512, bias=True)
            (v_proj): Linear(in_features=3584, out_features=512, bias=True)
            (o_proj): Linear(in_features=3584, out_features=3584, bias=False)
            (rotary_emb): Qwen2_5OmniRotaryEmbedding()
          )
          (mlp): Qwen2MLP(
            (gate_proj): Linear(in_features=3584, out_features=18944, bias=False)
            (up_proj): Linear(in_features=3584, out_features=18944, bias=False)
            (down_proj): Linear(in_features=18944, out_features=3584, bias=False)
            (act_fn): SiLU()
          )
          (input_layernorm): Qwen2RMSNorm((3584,), eps=1e-06)
          (post_attention_layernorm): Qwen2RMSNorm((3584,), eps=1e-06)
        )
      )
      (norm): Qwen2RMSNorm((3584,), eps=1e-06)
      (rotary_emb): Qwen2_5OmniRotaryEmbedding()
    )
    (lm_head): Linear(in_features=3584, out_features=152064, bias=False)
  )
  (talker): Qwen2_5OmniTalkerForConditionalGeneration(
    (thinker_to_talker_proj): Linear(in_features=3584, out_features=896, bias=True)
    (model): Qwen2_5OmniTalkerModel(
      (embed_tokens): Embedding(8448, 3584)
      (layers): ModuleList(
        (0-23): 24 x Qwen2_5OmniDecoderLayer(
          (self_attn): Qwen2_5OmniAttention(
            (q_proj): Linear(in_features=896, out_features=1536, bias=True)
            (k_proj): Linear(in_features=896, out_features=512, bias=True)
            (v_proj): Linear(in_features=896, out_features=512, bias=True)
            (o_proj): Linear(in_features=1536, out_features=896, bias=False)
            (rotary_emb): Qwen2_5OmniRotaryEmbedding()
          )
          (mlp): Qwen2MLP(
            (gate_proj): Linear(in_features=896, out_features=18944, bias=False)
            (up_proj): Linear(in_features=896, out_features=18944, bias=False)
            (down_proj): Linear(in_features=18944, out_features=896, bias=False)
            (act_fn): SiLU()
          )
          (input_layernorm): Qwen2RMSNorm((896,), eps=1e-06)
          (post_attention_layernorm): Qwen2RMSNorm((896,), eps=1e-06)
        )
      )
      (norm): Qwen2RMSNorm((896,), eps=1e-06)
      (rotary_emb): Qwen2_5OmniRotaryEmbedding()
    )
    (codec_head): Linear(in_features=896, out_features=8448, bias=False)
  )
  (token2wav): Qwen2_5OmniToken2WavModel(
    ...
  )
)

```


###### MMS ASR示例代码：
```python
# load
processor = AutoProcessor.from_pretrained(model_id, target_lang=target_lang)
model = Wav2Vec2ForCTC.from_pretrained(model_id, target_lang=target_lang, ignore_mismatched_sizes=True)

# infer
sample = load_audio_file(audio_path, target_sr=16000)
inputs = processor(sample, sampling_rate=16000, return_tensors="pt")
with torch.no_grad():
    outputs = model(**inputs).logits
ids = torch.argmax(outputs, dim=-1)[0]
transcription = processor.decode(ids)
```

###### MMS 模型结构
```bash
Wav2Vec2ForCTC(
  (wav2vec2): Wav2Vec2Model(
    (feature_extractor): Wav2Vec2FeatureEncoder(
      (conv_layers): ModuleList(
        (0): Wav2Vec2LayerNormConvLayer(
          (conv): Conv1d(1, 512, kernel_size=(10,), stride=(5,))
          (layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
          (activation): GELUActivation()
        )
        (1-4): 4 x Wav2Vec2LayerNormConvLayer(
          (conv): Conv1d(512, 512, kernel_size=(3,), stride=(2,))
          (layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
          (activation): GELUActivation()
        )
        (5-6): 2 x Wav2Vec2LayerNormConvLayer(
          (conv): Conv1d(512, 512, kernel_size=(2,), stride=(2,))
          (layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
          (activation): GELUActivation()
        )
      )
    )
    (feature_projection): Wav2Vec2FeatureProjection(
      (layer_norm): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      (projection): Linear(in_features=512, out_features=1280, bias=True)
      (dropout): Dropout(p=0.05, inplace=False)
    )
    (encoder): Wav2Vec2EncoderStableLayerNorm(
      (pos_conv_embed): Wav2Vec2PositionalConvEmbedding(
        (conv): ParametrizedConv1d(
          1280, 1280, kernel_size=(128,), stride=(1,), padding=(64,), groups=16
          (parametrizations): ModuleDict(
            (weight): ParametrizationList(
              (0): _WeightNorm()
            )
          )
        )
        (padding): Wav2Vec2SamePadLayer()
        (activation): GELUActivation()
      )
      (layer_norm): LayerNorm((1280,), eps=1e-05, elementwise_affine=True)
      (dropout): Dropout(p=0.05, inplace=False)
      (layers): ModuleList(
        (0-47): 48 x Wav2Vec2EncoderLayerStableLayerNorm(
          (attention): Wav2Vec2Attention(
            (k_proj): Linear(in_features=1280, out_features=1280, bias=True)
            (v_proj): Linear(in_features=1280, out_features=1280, bias=True)
            (q_proj): Linear(in_features=1280, out_features=1280, bias=True)
            (out_proj): Linear(in_features=1280, out_features=1280, bias=True)
          )
          (dropout): Dropout(p=0.05, inplace=False)
          (layer_norm): LayerNorm((1280,), eps=1e-05, elementwise_affine=True)
          (feed_forward): Wav2Vec2FeedForward(
            (intermediate_dropout): Dropout(p=0.05, inplace=False)
            (intermediate_dense): Linear(in_features=1280, out_features=5120, bias=True)
            (intermediate_act_fn): GELUActivation()
            (output_dense): Linear(in_features=5120, out_features=1280, bias=True)
            (output_dropout): Dropout(p=0.05, inplace=False)
          )
          (final_layer_norm): LayerNorm((1280,), eps=1e-05, elementwise_affine=True)
          (adapter_layer): Wav2Vec2AttnAdapterLayer(
            (norm): LayerNorm((1280,), eps=1e-05, elementwise_affine=True)
            (linear_1): Linear(in_features=1280, out_features=16, bias=True)
            (act_fn): ReLU()
            (linear_2): Linear(in_features=16, out_features=1280, bias=True)
          )
        )
      )
    )
  )
  (dropout): Dropout(p=0.05, inplace=False)
  (lm_head): Linear(in_features=1280, out_features=65, bias=True)
)
```

MMS tokenizer
```bash
Wav2Vec2Processor:
- feature_extractor: Wav2Vec2FeatureExtractor {
  "do_normalize": true,
  "feature_extractor_type": "Wav2Vec2FeatureExtractor",
  "feature_size": 1,
  "padding_side": "right",
  "padding_value": 0,
  "processor_class": "Wav2Vec2Processor",
  "return_attention_mask": true,
  "sampling_rate": 16000
}

- tokenizer: Wav2Vec2CTCTokenizer(name_or_path='/home/<USER>/zszd/lh/MMS_LLM/MMS_adapter_fintune_work/mms_1b_all', vocab_size=65, model_max_length=1000000000000000019884624838656, is_fast=False, padding_side='right', truncation_side='right', special_tokens={'bos_token': '<s>', 'eos_token': '</s>', 'unk_token': '<unk>', 'pad_token': '<pad>'}, clean_up_tokenization_spaces=True, added_tokens_decoder={
	0: AddedToken("<pad>", rstrip=True, lstrip=True, single_word=False, normalized=False, special=False),
	1: AddedToken("<s>", rstrip=True, lstrip=True, single_word=False, normalized=False, special=False),
	2: AddedToken("</s>", rstrip=True, lstrip=True, single_word=False, normalized=False, special=False),
	3: AddedToken("<unk>", rstrip=True, lstrip=True, single_word=False, normalized=False, special=False),
}
)

{
  "processor_class": "Wav2Vec2Processor"
}

```