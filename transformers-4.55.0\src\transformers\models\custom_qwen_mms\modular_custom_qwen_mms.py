# coding=utf-8
# Copyright 2025 The Custom Qwen-MMS team and the HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""PyTorch Custom Qwen-MMS model (Audio, Image, Video) with MMS Wav2Vec2 audio encoder."""

import math
from dataclasses import dataclass
from typing import Any, Callable, Optional, Union

import numpy as np
import torch
import torch.nn.functional as F
import torch.utils.checkpoint
from torch import nn
from torch.nn import Parameter

from transformers.models.llama.modeling_llama import rotate_half
from transformers.models.qwen2_5_vl.configuration_qwen2_5_vl import Qwen2_5_VLVisionConfig
from transformers.models.qwen2_5_vl.modeling_qwen2_5_vl import (
    Qwen2_5_VisionTransformerPretrainedModel,
    Qwen2_5_VLAttention,
    Qwen2_5_VLMLP,
    Qwen2_5_VLPreTrainedModel,
    Qwen2_5_VLTextModel,
    Qwen2_5_VLVisionBlock,
    eager_attention_forward,
)
from transformers.models.wav2vec2.modeling_wav2vec2 import Wav2Vec2Model
from transformers.models.wav2vec2.configuration_wav2vec2 import Wav2Vec2Config
from transformers.models.qwen2_vl.modeling_qwen2_vl import Qwen2VLRotaryEmbedding

from ...cache_utils import Cache
from ...configuration_utils import PretrainedConfig, layer_type_validation
from ...generation import GenerationMixin
from ...modeling_flash_attention_utils import is_flash_attn_available
from ...modeling_outputs import BaseModelOutput, ModelOutput
from ...modeling_rope_utils import rope_config_validation
from ...modeling_utils import ALL_ATTENTION_FUNCTIONS
from ...processing_utils import Unpack
from ...utils import (
    TransformersKwargs,
    auto_docstring,
    check_torch_load_is_safe,
    logging,
)
from ...utils.hub import cached_file


if is_flash_attn_available():
    from ...modeling_flash_attention_utils import apply_rotary_emb, flash_attn_varlen_func
else:
    flash_attn_varlen_func = None
    apply_rotary_emb = None


logger = logging.get_logger(__name__)


class CustomQwenMMSVisionEncoderConfig(Qwen2_5_VLVisionConfig):
    r"""
    This is the configuration class to store the configuration of a [`CustomQwenMMSVisionEncoder`]. It is used to instantiate a
    Custom Qwen-MMS vision encoder according to the specified arguments, defining the model architecture. Instantiating a
    configuration with the defaults will yield a similar configuration to that of the vision encoder of the Qwen2.5-VL
    architecture.

    Configuration objects inherit from [`PretrainedConfig`] and can be used to control the model outputs. Read the
    documentation from [`PretrainedConfig`] for more information.

    Args:
        depth (`int`, *optional*, defaults to 32):
            Number of layers (depth) in the model.
        hidden_size (`int`, *optional*, defaults to 3584):
            The size of the hidden layers.
        hidden_act (`str`, *optional*, defaults to `"quick_gelu"`):
            The non-linear activation function used in the model. Supported options include `"quick_gelu"` and others as applicable.
        mlp_ratio (`float`, *optional*, defaults to 4):
            The ratio used to determine the size of the MLP (Multi-Layer Perceptron) hidden layer.
        num_heads (`int`, *optional*, defaults to 16):
            Number of attention heads for each attention layer.
        in_channels (`int`, *optional*, defaults to 3):
            Number of input channels.
        patch_size (`int`, *optional*, defaults to 14):
            The size of the patches extracted from the input.
        spatial_merge_size (`int`, *optional*, defaults to 2):
            The size used for merging spatial dimensions.
        temporal_patch_size (`int`, *optional*, defaults to 2):
            The size used for patches along the temporal dimension.

    Example:

    ```python
    >>> from transformers import CustomQwenMMSVisionEncoderConfig, CustomQwenMMSVisionEncoder

    >>> # Initializing a CustomQwenMMSVisionEncoderConfig
    >>> configuration = CustomQwenMMSVisionEncoderConfig()

    >>> # Initializing a CustomQwenMMSVisionEncoder (with random weights)
    >>> model = CustomQwenMMSVisionEncoder(configuration)

    >>> # Accessing the model configuration
    >>> configuration = model.config
    ```"""

    model_type = "custom_qwen_mms_vision_encoder"
    base_config_key = "vision_config"

    def __init__(
        self,
        depth=32,
        hidden_size=3584,
        hidden_act="silu",
        intermediate_size=3420,
        num_heads=16,
        in_channels=3,
        patch_size=14,
        spatial_merge_size=2,
        temporal_patch_size=2,
        window_size=112,
        out_hidden_size=3584,
        fullatt_block_indexes=[7, 15, 23, 31],
        initializer_range=0.02,
        **kwargs,
    ):
        super().__init__(
            depth=depth,
            hidden_size=hidden_size,
            hidden_act=hidden_act,
            intermediate_size=intermediate_size,
            num_heads=num_heads,
            in_channels=in_channels,
            patch_size=patch_size,
            spatial_merge_size=spatial_merge_size,
            temporal_patch_size=temporal_patch_size,
            window_size=window_size,
            out_hidden_size=out_hidden_size,
            fullatt_block_indexes=fullatt_block_indexes,
            initializer_range=initializer_range,
            **kwargs,
        )


class CustomQwenMMSAudioEncoderConfig(PretrainedConfig):
    r"""
    This is the configuration class to store the configuration of a [`CustomQwenMMSAudioEncoder`]. It is used to instantiate a
    Custom Qwen-MMS audio encoder according to the specified arguments, defining the model architecture. This configuration
    wraps the MMS Wav2Vec2 model and adds adaptation layers to match Qwen's expected output dimensions.

    Configuration objects inherit from [`PretrainedConfig`] and can be used to control the model outputs. Read the
    documentation from [`PretrainedConfig`] for more information.

    Args:
        mms_config (`dict` or `Wav2Vec2Config`, *optional*):
            Configuration for the underlying MMS Wav2Vec2 model.
        output_dim (`int`, *optional*, defaults to 3584):
            The output dimension that matches Qwen's expected audio feature dimension.
        adapter_hidden_size (`int`, *optional*, defaults to 1280):
            The hidden size of the MMS Wav2Vec2 model output (typically 1280).
        adapter_type (`str`, *optional*, defaults to "linear"):
            Type of adapter layer. Can be "linear" or "mlp".
        dropout (`float`, *optional*, defaults to 0.1):
            Dropout rate for the adapter layers.
        layer_norm (`bool`, *optional*, defaults to True):
            Whether to apply layer normalization after the adapter.

    Example:

    ```python
    >>> from transformers import CustomQwenMMSAudioEncoderConfig, CustomQwenMMSAudioEncoder

    >>> # Initializing a CustomQwenMMSAudioEncoderConfig
    >>> configuration = CustomQwenMMSAudioEncoderConfig()

    >>> # Initializing a CustomQwenMMSAudioEncoder (with random weights)
    >>> model = CustomQwenMMSAudioEncoder(configuration)

    >>> # Accessing the model configuration
    >>> configuration = model.config
    ```"""

    model_type = "custom_qwen_mms_audio_encoder"
    base_config_key = "audio_config"

    def __init__(
        self,
        mms_config=None,
        output_dim=3584,
        adapter_hidden_size=1280,
        adapter_type="linear",
        dropout=0.1,
        layer_norm=True,
        **kwargs,
    ):
        super().__init__(**kwargs)
        
        if mms_config is None:
            # Default MMS Wav2Vec2 configuration
            self.mms_config = Wav2Vec2Config()
        elif isinstance(mms_config, dict):
            self.mms_config = Wav2Vec2Config(**mms_config)
        else:
            self.mms_config = mms_config
            
        self.output_dim = output_dim
        self.adapter_hidden_size = adapter_hidden_size
        self.adapter_type = adapter_type
        self.dropout = dropout
        self.layer_norm = layer_norm


class CustomQwenMMSTextConfig(PretrainedConfig):
    r"""
    This is the configuration class to store the configuration of a [`CustomQwenMMSThinkerForConditionalGeneration`]. It is used to instantiate an
    Custom Qwen-MMS model according to the specified arguments, defining the model architecture. Instantiating a configuration
    with the defaults will yield a similar configuration to that of the Custom Qwen-MMS.

    Configuration objects inherit from [`PretrainedConfig`] and can be used to control the model outputs. Read the
    documentation from [`PretrainedConfig`] for more information.

    Args:
        vocab_size (`int`, *optional*, defaults to 152064):
            Vocabulary size of the Custom Qwen-MMS model. Defines the number of different tokens that can be represented by the
            `inputs_ids` passed when calling [`CustomQwenMMSModel`]
        hidden_size (`int`, *optional*, defaults to 3584):
            Dimension of the hidden representations.
        intermediate_size (`int`, *optional*, defaults to 18944):
            Dimension of the MLP representations.
        num_hidden_layers (`int`, *optional*, defaults to 28):
            Number of hidden layers in the Transformer decoder.
        num_attention_heads (`int`, *optional*, defaults to 28):
            Number of attention heads for each attention layer in the Transformer decoder.
        num_key_value_heads (`int`, *optional*, defaults to 4):
            This is the number of key_value heads that should be used to implement Grouped Query Attention. If
            `num_key_value_heads=num_attention_heads`, the model will use Multi Head Attention (MHA), if
            `num_key_value_heads=1` the model will use Multi Query Attention (MQA) otherwise GQA is used. When
            converting a multi-head checkpoint to a GQA checkpoint, each group key and value head should be constructed
            by meanpooling all the original heads within that group. For more details checkout [this
            paper](https://arxiv.org/pdf/2305.13245.pdf). If it is not specified, will default to `32`.
        hidden_act (`str` or `function`, *optional*, defaults to `"silu"`):
            The non-linear activation function (function or string) in the decoder.
        max_position_embeddings (`int`, *optional*, defaults to 32768):
            The maximum sequence length that this model might ever be used with. Custom Qwen-MMS's attention allows sequence
            of up to 32768 tokens.
        initializer_range (`float`, *optional*, defaults to 0.02):
            The standard deviation of the truncated_normal_initializer for initializing all weight matrices.
        rms_norm_eps (`float`, *optional*, defaults to 1e-06):
            The epsilon used by the rms normalization layers.
        use_cache (`bool`, *optional*, defaults to `True`):
            Whether or not the model should return the last key/values attentions (not used by all models). Only
            relevant if `config.is_decoder=True`.
        pad_token_id (`int`, *optional*):
            Padding token id.
        bos_token_id (`int`, *optional*, defaults to 151643):
            Beginning of stream token id.
        eos_token_id (`int`, *optional*, defaults to 151645):
            End of stream token id.
        tie_word_embeddings (`bool`, *optional*, defaults to `False`):
            Whether to tie weight embeddings
        rope_theta (`float`, *optional*, defaults to 1000000.0):
            The base period of the RoPE embeddings.
        rope_scaling (`Dict`, *optional*):
            Dictionary containing the scaling configuration for the RoPE embeddings. NOTE: if you apply new rope type
            and you expect the model to work on longer `max_position_embeddings`, we recommend you to update this value
            accordingly.
            Expected contents:
                `rope_type` (`str`):
                    The sub-variant of RoPE to use. Can be one of ['default', 'linear', 'dynamic', 'yarn', 'longrope',
                    'llama3'], with 'default' being the original RoPE implementation.
                `factor` (`float`, *optional*):
                    Used with all rope types except 'default'. The scaling factor to apply to the RoPE embeddings. In
                    most scaling types, a `factor` of x will enable the model to handle sequences of length x *
                    original maximum pre-trained length.
                `original_max_position_embeddings` (`int`, *optional*):
                    Used with 'dynamic', 'longrope' and 'llama3'. The original max position embeddings used during
                    pretraining.
                `attention_factor` (`float`, *optional*):
                    Used with 'yarn' and 'longrope'. The scaling factor to be applied on the attention
                    computation. If unspecified, it defaults to value recommended by the implementation, using the
                    `factor` field to infer the suggested value.
                `beta_fast` (`float`, *optional*):
                    Only used with 'yarn'. Parameter to set the boundary for extrapolation (only) in the linear
                    ramp function. If unspecified, it defaults to 32.
                `beta_slow` (`float`, *optional*):
                    Only used with 'yarn'. Parameter to set the boundary for interpolation (only) in the linear
                    ramp function. If unspecified, it defaults to 1.
                `short_factor` (`List[float]`, *optional*):
                    Only used with 'longrope'. The scaling factor to be applied to short contexts (<
                    `original_max_position_embeddings`). Must be a list of numbers with the same length as the hidden
                    size divided by the number of attention heads divided by 2
                `long_factor` (`List[float]`, *optional*):
                    Only used with 'longrope'. The scaling factor to be applied to long contexts (<
                    `original_max_position_embeddings`). Must be a list of numbers with the same length as the hidden
                    size divided by the number of attention heads divided by 2
                `low_freq_factor` (`float`, *optional*):
                    Only used with 'llama3'. Scaling factor applied to low frequency components of the RoPE
                `high_freq_factor` (`float`, *optional*):
                    Only used with 'llama3'. Scaling factor applied to high frequency components of the RoPE
        sliding_window (`int`, *optional*):
            Sliding window attention window size. If not specified, will default to `None`.
        attention_dropout (`float`, *optional*, defaults to 0.0):
            The dropout ratio for the attention probabilities.
        layer_types (`List[str]`, *optional*):
            Defines the layer types in the model. If specified, it should be a list of strings with the same length as
            `num_hidden_layers`. Each string should be one of the supported layer types. If not specified, all layers
            will be of the default type.

    ```python
    >>> from transformers import CustomQwenMMSModel, CustomQwenMMSTextConfig

    >>> # Initializing a Custom Qwen-MMS custom_qwen_mms-7b style configuration
    >>> configuration = CustomQwenMMSTextConfig()

    >>> # Initializing a model from the custom_qwen_mms-7b style configuration
    >>> model = CustomQwenMMSModel(configuration)

    >>> # Accessing the model configuration
    >>> configuration = model.config
    ```"""

    model_type = "custom_qwen_mms_text"
    keys_to_ignore_at_inference = ["past_key_values"]

    def __init__(
        self,
        vocab_size=152064,
        hidden_size=3584,
        intermediate_size=18944,
        num_hidden_layers=28,
        num_attention_heads=28,
        num_key_value_heads=4,
        hidden_act="silu",
        max_position_embeddings=32768,
        initializer_range=0.02,
        rms_norm_eps=1e-6,
        use_cache=True,
        pad_token_id=None,
        bos_token_id=151643,
        eos_token_id=151645,
        tie_word_embeddings=False,
        rope_theta=1000000.0,
        rope_scaling=None,
        sliding_window=None,
        attention_dropout=0.0,
        layer_types=None,
        **kwargs,
    ):
        self.vocab_size = vocab_size
        self.max_position_embeddings = max_position_embeddings
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.num_hidden_layers = num_hidden_layers
        self.num_attention_heads = num_attention_heads
        self.sliding_window = sliding_window
        self.layer_types = layer_types

        # for backward compatibility
        if num_key_value_heads is None:
            num_key_value_heads = num_attention_heads

        self.num_key_value_heads = num_key_value_heads
        self.hidden_act = hidden_act
        self.initializer_range = initializer_range
        self.rms_norm_eps = rms_norm_eps
        self.use_cache = use_cache
        self.rope_theta = rope_theta
        self.rope_scaling = rope_scaling
        self.attention_dropout = attention_dropout

        # Validate the correctness of rotary position embeddings parameters
        rope_config_validation(self)

        if self.layer_types is not None and len(self.layer_types) != self.num_hidden_layers:
            raise ValueError(
                f"The length of `layer_types` ({len(self.layer_types)}) must equal `num_hidden_layers` "
                f"({self.num_hidden_layers})"
            )

        if self.layer_types is None:
            self.layer_types = ["attention"] * self.num_hidden_layers
        else:
            self.layer_types = [
                layer_type if layer_type in ALL_ATTENTION_FUNCTIONS else "attention"
                for i in range(self.num_hidden_layers)
            ]
        layer_type_validation(self.layer_types)

        super().__init__(
            pad_token_id=pad_token_id,
            bos_token_id=bos_token_id,
            eos_token_id=eos_token_id,
            tie_word_embeddings=tie_word_embeddings,
            **kwargs,
        )
